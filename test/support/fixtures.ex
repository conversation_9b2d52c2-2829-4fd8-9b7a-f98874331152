defmodule Test.Fixtures do
  @moduledoc """
  Provides consistent fixture data for doctests and tests.

  This module loads predefined fixture data into the database to ensure
  doctests have consistent, predictable data to work with.
  """

  @doc """
  Loads fixture data for the specified tables.

  ## Examples

      Test.Fixtures.load(:users)
      Test.Fixtures.load([:users, :posts])
  """
  def load(table_names) when is_list(table_names) do
    Enum.each(table_names, &load/1)
  end

  def load(table_name) when is_atom(table_name) do
    case table_name do
      :users -> load_users()
      :posts -> load_posts()
      _ -> raise ArgumentError, "Unknown fixture table: #{table_name}"
    end
  end

  @doc """
  Loads user fixtures into the users table.

  Creates 3 users with predictable data:
  - User with ID 1: "<PERSON>", "<EMAIL>", age 30, active: true
  - User with ID 2: "<PERSON>", "<EMAIL>", age 25, active: true
  - User with ID 3: "<PERSON>", "<EMAIL>", age 35, active: false
  """
  def load_users do
    repo = MyApp.Repo

    # Clear existing data
    repo.delete_all("users")

    # Insert fixture data with explicit IDs
    users = [
      %{id: 1, name: "<PERSON>", email: "<EMAIL>", age: 30, active: true, settings: %{}},
      %{
        id: 2,
        name: "<PERSON>",
        email: "<EMAIL>",
        age: 25,
        active: true,
        settings: %{}
      },
      %{
        id: 3,
        name: "Bob <PERSON>",
        email: "<EMAIL>",
        age: 35,
        active: false,
        settings: %{}
      }
    ]

    repo.insert_all("users", users, returning: false)

    # Reset sequence to ensure next auto-generated ID is 4
    reset_sequence(repo, "users", 4)
  end

  @doc """
  Loads post fixtures into the posts table.

  Creates 4 posts with predictable data:
  - Post with ID 1: "First Post" by John (user_id: 1), published: true, view_count: 100
  - Post with ID 2: "Second Post" by Jane (user_id: 2), published: true, view_count: 50
  - Post with ID 3: "Draft Post" by John (user_id: 1), published: false, view_count: 0
  - Post with ID 4: "Another Post" by Bob (user_id: 3), published: true, view_count: 25
  """
  def load_posts do
    repo = MyApp.Repo

    # Clear existing data
    repo.delete_all("posts")

    # Insert fixture data with explicit IDs
    posts = [
      %{
        id: 1,
        title: "First Post",
        body: "This is the first post content.",
        user_id: 1,
        published: true,
        view_count: 100
      },
      %{
        id: 2,
        title: "Second Post",
        body: "This is the second post content.",
        user_id: 2,
        published: true,
        view_count: 50
      },
      %{
        id: 3,
        title: "Draft Post",
        body: "This is a draft post.",
        user_id: 1,
        published: false,
        view_count: 0
      },
      %{
        id: 4,
        title: "Another Post",
        body: "This is another post.",
        user_id: 3,
        published: true,
        view_count: 25
      }
    ]

    Enum.each(posts, fn post_attrs ->
      repo.insert_all("posts", [post_attrs],
        on_conflict: :replace_all,
        conflict_target: :id,
        returning: false
      )
    end)

    # Reset sequence to ensure next auto-generated ID is 5
    reset_sequence(repo, "posts", 5)
  end

  # Private helper to reset database sequences
  defp reset_sequence(repo, table_name, next_id) do
    adapter = repo.__adapter__()

    case adapter do
      Ecto.Adapters.Postgres ->
        sequence_name = "#{table_name}_id_seq"
        repo.query!("SELECT setval('#{sequence_name}', #{next_id - 1})")

      Ecto.Adapters.SQLite3 ->
        # SQLite uses AUTOINCREMENT which doesn't need manual sequence reset
        # The next insert will automatically use the next available ID
        :ok

      _ ->
        # For other adapters, we'll skip sequence reset
        :ok
    end
  end
end
